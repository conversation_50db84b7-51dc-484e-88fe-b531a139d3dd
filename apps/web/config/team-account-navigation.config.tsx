import {
  Barcode,
  Blocks,
  Brush,
  Calendar<PERSON>heck,
  CreditCard,
  FileChartColumn,
  Images,
  LayoutDashboard,
  Megaphone,
  Settings,
  User,
  UserRound,
  Users,
  ChartColumn
} from 'lucide-react';

import { NavigationConfigSchema } from '@kit/ui/navigation-schema';

import featureFlagsConfig from '~/config/feature-flags.config';
import pathsConfig from '~/config/paths.config';

const iconClasses = 'w-4';

const getRoutes = (account: string) => [
  // {
  //   label: '',
  //   collapsible: false,
  //   children: [
  //     {
  //       label: 'Inbox',
  //       path: pathsConfig.app.accountHome.replace('[account]', account),
  //       Icon: <Inbox className={iconClasses} />,
  //       end: true,
  //     },
  //   ],
  //   Icon: <Images className={iconClasses} />,
  //   path: createPath(pathsConfig.app.accountBrandAssets, account),
  //   end: true,
  // },
  {
    label: 'Workspace',
    collapsible: true,
    children: [
      {
        label: 'Dashboard',
        path: createPath(pathsConfig.app.home, account),
        Icon: <LayoutDashboard className={iconClasses} />,
        // Icon: <LayoutDashboard className={iconClasses} />,
        end: true,
      },
      // {
      //   label: 'Campaigns',
      //   path: createPath(pathsConfig.app.campaigns, account),
      //   Icon: <Megaphone className={iconClasses} />,
      //   // Icon: <LayoutDashboard className={iconClasses} />,
      //   end: true,
      // },  
      {
        label: 'Tasks',
        path: createPath(pathsConfig.app.tasks, account),
        Icon: <CalendarCheck className={iconClasses} />,
        end: true,
      },
      // {
      //   label: 'Content Studio',
      //   path: createPath(pathsConfig.app.studio, account),
      //   Icon: <Barcode className={iconClasses} />,
      //   end: true,
      // },
      {
        label: 'Analytics',
        path: createPath(pathsConfig.app.analytics, account),
        Icon: <ChartColumn className={iconClasses} />,
        end: true,
      },
    ],
  },
  {
    label: 'Brand',
    collapsible: true,
    children: [
      {
        label: 'common:routes.brand',
        path: createPath(pathsConfig.app.accountBrand, account),
        Icon: <Brush className={iconClasses} />,
        end: true,
      },
      {
        label: 'Customer Profiles',
        path: createPath(pathsConfig.app.personas, account),
        Icon: <UserRound className={iconClasses} />,
        end: true,
      },
      {
        label: 'Market Research',
        path: createPath(pathsConfig.app.personalAccountMarketResearch, account),
        Icon: <FileChartColumn className={iconClasses} />,
        end: true,
      },
      {
        // label: 'common:routes.brandAssets',
        label: 'Asset Library',
        path: createPath(pathsConfig.app.accountBrandAssets, account),
        Icon: <Images className={iconClasses} />,
        end: true,
      },
      {
        label: 'Product Information',
        path: createPath(pathsConfig.app.accountProductInformation, account),
        Icon: <Barcode className={iconClasses} />,
        end: true,
      },
    ],
  },
  // {
  //   label: 'Views',
  //   collapsible: true,
  //   children: [
  //     {
  //       // label: 'common:routes.brandAssets',
  //       label: 'Calendar',
  //       path: createPath(pathsConfig.app.accountBrandAssets, account),
  //       Icon: <Calendar className={iconClasses} />,
  //       end: true,
  //     },
  //     {
  //       label: 'Kanban',
  //       path: createPath(pathsConfig.app.accountBrandAssets, account),
  //       Icon: <CircuitBoard className={iconClasses} />,
  //       end: true,
  //     },
  //     {
  //       label: 'List',
  //       path: createPath(pathsConfig.app.accountBrandAssets, account),
  //       Icon: <List className={iconClasses} />,
  //       end: true,
  //     },
  //   ],
  // },
  {
    label: 'common:routes.settings',
    collapsible: false,
    children: [
      {
        label: 'common:routes.settings',
        path: createPath(pathsConfig.app.accountMembers, account), // Default to Members page
        Icon: <Settings className={iconClasses} />,
        end: true,
      },
    ],
  },
];

export function getTeamAccountSidebarConfig(account: string) {
  return NavigationConfigSchema.parse({
    routes: getRoutes(account),
    style: process.env.NEXT_PUBLIC_TEAM_NAVIGATION_STYLE,
  });
}

function createPath(path: string, account: string) {
  return path.replace('[account]', account);
}
