// schema.ts
import {
  createSchema,
  table,
  string,
  boolean,
  json,
  number,
  enumeration,
  ExpressionBuilder,
  ANYONE_CAN,
  relationships,
} from '@rocicorp/zero';

import {
  definePermissions,
  ANYONE_CAN_DO_ANYTHING,
} from '@rocicorp/zero';

// The decoded value of your JWT.
type AuthData = {
  // The logged-in user.
  sub: string;
};

// Define the `accounts` table
const accounts = table('accounts').columns({
  id: string(), // UUID, primary key
  primary_owner_user_id: string(), // foreign key
  name: string(),
  website: string().optional(),
  slug: string().optional(),
  email: string().optional(),
  is_personal_account: boolean(),
  updated_at: number().optional(), // timestamp
  created_at: number().optional(), // timestamp
  created_by: string().optional(), // UUID
  updated_by: string().optional(), // UUID
  picture_url: string().optional(),
  public_data: json().optional(),
}).primaryKey('id');

export const company_brand = table('company_brand').columns({
  id: string(),
  created_at: number().from('created_at').optional(),
  company_id: string().from('company_id'),
  has_brand_setup: boolean().from('has_brand_setup').optional(),
  updated_at: number().from('updated_at').optional(), // `timestamp` → number (UNIX)
  brand_name: string().from('brand_name').optional(),
  is_generating: boolean().from('is_generating').optional(),
  error_generating: boolean().from('error_generating').optional(),
  brand_profile: json().from('brand_profile').optional(),
  messaging_strategy: json().from('messaging_strategy').optional(),
  visual_identity: json().from('visual_identity').optional(),
  product_catalog: json().from('product_catalog').optional(),
  prompt_library: json().from('prompt_library').optional(),
}).primaryKey('id');

// Define the `icps` table
const icps = table('icps').columns({
  id: string(), // UUID, primary key
  name: string().optional(),
  created_at: number().optional(), // timestamp
  updated_at: number().optional(), // timestamp
  company_id: string().optional(), // foreign key to accounts
  data: json().optional(), // JSONB field containing ICP data
  is_generating: boolean().optional(),
  error_generating: boolean().optional(),
  reference_material: json().optional(),
  reference_description: string().optional(),
  linkedInUrls: json().optional(),
  withLinkedIn: boolean().optional(),
  withAi: boolean().optional()
}).primaryKey('id');

// Define the `personas` table
const personas = table('personas').columns({
  id: string(), // UUID, primary key
  name: string().optional(),
  created_at: number().optional(), // timestamp
  updated_at: number().optional(), // timestamp
  company_id: string().optional(), // foreign key to accounts
  icp_id: string().optional(), // foreign key to icps
  data: json().optional(), // JSONB field containing ICP data
  is_generating: boolean().optional(),
  error_generating: boolean().optional(),
}).primaryKey('id');

// const invitations = table('invitations').columns({
//   id: number(), // Zero doesn't support serial — we use string IDs (you can map serial to string safely)
//   email: string(),
//   account_id: string(), // foreign key
//   invited_by: string(), // foreign key
//   role: string(),
//   invite_token: string(),
//   created_at: number(),
//   updated_at: number(),
//   expires_at: number(),
// }).primaryKey('id');

const memberships = table('accounts_memberships').columns({
  user_id: string(),
  account_id: string(),
  account_role: string(),
  created_at: number().optional(),
  updated_at: number().optional(),
}).primaryKey('user_id', 'account_id');

// Define the subscriptions table for trial management
const subscriptions = table('subscriptions').columns({
  id: string(), // primary key
  account_id: string(), // foreign key to accounts
  billing_customer_id: number().optional(),
  active: boolean(),
  status: enumeration<'trialing' | 'active' | 'canceled' | 'past_due' | 'unpaid' | 'incomplete' | 'incomplete_expired' | 'paused'>(),
  billing_provider: enumeration<'stripe' | 'lemonsqueezy'>(),
  cancel_at_period_end: boolean(),
  currency: string().optional(),
  period_starts_at: number(),
  period_ends_at: number(),
  trial_starts_at: number().optional(),
  trial_ends_at: number().optional(),
  created_at: number().optional(),
  updated_at: number().optional(),
}).primaryKey('id');

export const company_campaigns = table('company_campaigns').columns({
  id: string(),
  created_at: number(),
  company_id: string().optional(),
  user_id: string().optional(),
  name: string().optional(),
  slug: string().optional(),
  messaging: string().optional(),
  value_prop: string().optional(),
  objective: string().optional(),
  identity: string().optional(),
  kpis: string().optional(),
  objectives: string().optional(), //no longer needed, do not use
  guidelines: string().optional(),
  personas: string().optional(),
  personality: string().optional(),
  targetAudience: string().optional(),
  tone: string().optional(),
  visualStyle: string().optional(),
  voice: string().optional(),
  documents: json().optional(),
  metadata: json().optional(),
  status: enumeration<'Draft' | 'Published' | 'Archived'>().optional(), // adjust as needed
  end_date: number().optional(), // Postgres `date`, can use string
  start_date: number().optional(),
  has_reached_summary: boolean().optional(),
  external_research: json().optional(),
  is_generating: boolean().optional(),
  error_generating: boolean().optional(),
  target_icps: json().optional(),
  target_personas: json().optional(),
}).primaryKey('id');

export const campaign_templates = table('campaign_templates').columns({
  id: string(),
  created_at: number(),
  title: string(),
  goal: string(),
  description: string(),
  style: json().optional(), // optional based on your data row (it was empty)
  image_url: string().optional(),
  duration_weeks: number().optional(),
}).primaryKey('id');

export const post_templates = table('post_templates').columns({
  id: string(),
  created_at: number().from('created_at'),
  title: string(),
  description: string().optional(),
  style: json().optional(),
  image_url: string().from('image_url').optional(),
  content_type: string().from('content_type').optional(),
  channel: string().from('channel').optional(),
}).primaryKey('id');


export const saved_research = table('saved_research').columns({
  id: string(),
  account_id: string(),
  icp_id: string(),
  persona_id: string().optional(),
  title: string().optional(),
  description: string(),
  source: string().optional(),
  relevance_score: number().optional(),
  research_type: string().optional(),
  time_filter: string().optional(),
  created_at: number().optional(),
  updated_at: number().optional(),
  source_url: string().optional(),
  topic: string().optional(),
  archived: boolean().optional(),
}).primaryKey('id');

export const user_cache = table('user_cache').columns({
  created_at: number(), // stored as UNIX timestamp in ms
  selected_campaign: string().optional(),
  user_id: string(),
  task_list_columns: json().optional(),
  task_sorting_state: json().optional(),
}).primaryKey('user_id');

export const products = table('products').columns({
  id: string(),
  created_at: number(), // stored as timestamptz → number (UNIX ms)
  updated_at: number(), // stored as timestamptz → number (UNIX ms)
  name: string(),
  description: string().optional(),
  target_audience: json().optional(), // stored as jsonb → json (array of ICP IDs)
  key_features: json().optional(), // stored as jsonb → json
  company_id: string(),
  custom_fields: json().optional() // stored as jsonb → json
}).primaryKey('id');

export const product_documents = table('product_documents').columns({
  id: string(),
  created_at: number(), // stored as timestamptz → number (UNIX ms)
  title: string(),
  file_path: string(),
  company_id: string(),
  content: string().optional(),
  file_type: string(),
  product_id: string().optional() // foreign key to products table
}).primaryKey('id');

export const site_research = table('site_research').columns({
  id: string(),
  company_id: string(),
  created_at: number(),
  icps: json().optional(),
  personal: json().optional(),
  urls: json().optional(),
  instruction: string().optional(),
  schema: json().optional(),
  enable_web_search: boolean().optional(),
  agent_mode: boolean().optional(),
  is_generating: boolean().optional(),
  error_generating: boolean().optional(),
  results: json().optional(),
}).primaryKey('id');

export const socials_research = table('socials_research').columns({
  id: string(),
  company_id: string(),
  created_at: number(),
  keywords: json().optional(),
  platform: string().optional(),
  is_generating: boolean().optional(),
  error_generating: boolean().optional(),
  results: json().optional(),
}).primaryKey('id');

export const generated_research = table('generated_research').columns({
  id: string(),
  account_id: string(),
  icp_id: string(),
  persona_id: string().optional(),
  research_type: string().optional(),
  time_filter: string().optional(),
  title: string().optional(),
  results: json().optional(), // Stores [] by default, using jsonb
  content_suggestions: json().optional(),
  created_at: number(),  // UNIX timestamp (milliseconds)
  updated_at: number(),  // UNIX timestamp (milliseconds)
  created_by: string().optional(),
  updated_by: string().optional(),
  is_generating: boolean().optional(),
  topic: string().optional(),
}).primaryKey('id');

export const company_content = table('company_content').columns({
  id: string(),
  campaign_id: string().optional(),
  company_id: string().optional(),
  idea_id: string().optional(),
  content_type: string().optional(),
  language: string().optional(),
  content: string().optional(),
  created_at: number(),
  updated_at: number(),
  image_path: string().optional(),
  content_template: string().optional(),
  image_url: string().optional(),
  channel: string().optional(),
  has_image: boolean().optional(),
  visual_description: string().optional(),
  avatar_script: string().optional(),
  avatar_voice_id: string().optional(),
  avatar_presenter_id: string().optional(),
  avatar_video_id: string().optional(),
  avatar_video_url: string().optional(),
  is_avatar_ready: boolean().optional(),
  has_avatar: boolean().optional(),
  is_generating: boolean().optional(),
  status: string().optional(),
  task_description: string().optional(),
  task_id: string().optional(),
  task_title: string().optional(),
  has_video_presentation: boolean().optional(),
  video_presentation_url: string().optional(),
  video_presentation_render_params: json().optional(),
  video_presentation_script: string().optional(),
  seo_keywords: json().optional(),
  trend_keywords: json().optional(),
  is_posted: boolean().optional(),
  is_scheduled: boolean().optional(),
  is_draft: boolean().optional(),
  visual_description_group: json().optional(),
  content_editor_template: json().optional(),
  scheduled_publishing_time: number().optional(),
  video_editor_overlays: json().optional(),
  video_editor_aspect_ratio: json().optional(),
  video_editor_player_dimensions: json().optional(),
  image_urls: json().optional(),
  archived: boolean().optional(),
  kanban_order: number().optional(),
  is_published: boolean().optional(),
  scheduled_at: number().optional(),
  published_by: string().optional(),
  published_url: string().optional(),
  assigned_to: string().optional(),
  schedule_date: number().optional(),
  ayrshare_post_id: string().optional(),
  is_paused: boolean().optional(),
  ayrshare_post_status: string().optional()
}).primaryKey('id');

export const ayrshare_user_profile = table('ayrshare_user_profile').columns({
  id: string(),
  user_id: string(),
  created_at: number(),
  title: string().optional(),
  refId: string(),
  profileKey: string(),
  messagingActive: boolean().optional(),
  company_id: string(),
  description: string().optional(),
  is_active: boolean(),
  profile_name: string().optional(),
  is_shared: boolean().optional(),
  permissions: json<{
    can_post: boolean;
    can_view: boolean;
    can_analytics: boolean;
  }>().optional(),
  updated_at: number(),
}).primaryKey('id');

export const post_engagement_details = table('post_engagement_details').columns({
  id: string(),
  platform_post_id: string(),
  engagement_info: json().optional(),
  is_generating: boolean().optional(),
  error_generating: boolean().optional(),
  profile_id: string(),
  engaged_users: json().optional(),
  company_id: string(),
}).primaryKey('id');

export const ayrshare_social_profiles = table('ayrshare_social_profiles').columns({
  id: string(),
  created_at: number().from('created_at').optional(),
  post_history: json().from('post_history').optional(),
  refId: string().from('refId').optional(),
  is_shared: boolean().from('is_shared').optional(),
  user_id: string().from('user_id').optional(),
  company_id: string().from('company_id').optional(),
  platform: string().from('platform').optional(),
  display_name: string().from('display_name').optional(),
  username: string().from('username').optional(),
  user_image: string().from('user_image').optional(),
  profile_url: string().from('profile_url').optional(),
  headline: string().from('headline').optional(),
  subscription_type: string().from('subscription_type').optional(),
  verified_type: string().from('verified_type').optional(),
  refresh_days_remaining: number().from('refresh_days_remaining').optional(),
  refresh_required: number().from('refresh_required').optional(),
  is_connected: boolean().from('is_connected').optional(),
  connected_at: number().from('connected_at').optional(),
  updated_at: number().from('updated_at').optional(),

}).primaryKey('id');

export const company_task_statuses = table('company_task_statuses').columns({
  id: string(),
  company_id: string(),
  name: string(),
  display_name: string(),
  color: string().optional(),
  status_order: number().optional(),
  icon: string().optional(),
  created_at: number().optional()
}).primaryKey('id');

// 👇 define how tables relate
const productsRelationships = relationships(products, ({ many }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
  product_documents: many({
    sourceField: ['id'],
    destField: ['product_id'],
    destSchema: product_documents,
  }),
}));

// 👇 define how tables relate
const productDocumentRelationships = relationships(product_documents, ({ many, one }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
  product: one({
    sourceField: ['product_id'],
    destField: ['id'],
    destSchema: products,
  }),
}));

// 👇 define how tables relate
const companyBrandRelationships = relationships(company_brand, ({ many }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

// 👇 define how tables relate
const generatedResearchRelationships = relationships(generated_research, ({ many }) => ({
  memberships: many({
    sourceField: ['account_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

// 👇 define how tables relate
const savedResearchRelationships = relationships(saved_research, ({ many }) => ({
  memberships: many({
    sourceField: ['account_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

const socialsResearchRelationships = relationships(socials_research, ({ many }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

// 👇 define how tables relate
const companyContentRelationships = relationships(company_content, ({ many }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

const companyCampaignRelationships = relationships(company_campaigns, ({ many }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

const icpRelationships = relationships(icps, ({ many }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

const personaRelationships = relationships(personas, ({ many }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

const ayrshareUserProfileRelationships = relationships(ayrshare_user_profile, ({ many }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
  // One user profile can have many social profiles (different platforms)
  socialProfiles: many({
    sourceField: ['refId'],
    destField: ['refId'],
    destSchema: ayrshare_social_profiles,
  }),
}));

const ayrshareSocialProfilesRelationships = relationships(ayrshare_social_profiles, ({ many, one }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
  // Each social profile belongs to one user profile
  userProfile: one({
    sourceField: ['refId'],
    destField: ['refId'],
    destSchema: ayrshare_user_profile,
  }),
}));

const companyTaskStatusesRelationships = relationships(company_task_statuses, ({ many }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

const postEngagementDetailsRelationships = relationships(post_engagement_details, ({ many }) => ({
  ayrshare_user_profile: many({
    sourceField: ['profile_id'],
    destField: ['id'],
    destSchema: ayrshare_user_profile,
  }),
}));

const siteResearchRelationships = relationships(site_research, ({ many }) => ({
  memberships: many({
    sourceField: ['company_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

const membershipsRelationships = relationships(memberships, ({ many }) => ({
  // This relationship finds all memberships associated with the same account.
  companyMemberships: many({
    sourceField: ['account_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

const subscriptionsRelationships = relationships(subscriptions, ({ many }) => ({
  memberships: many({
    sourceField: ['account_id'],
    destField: ['account_id'],
    destSchema: memberships,
  }),
}));

// Define the schema with tables
export const schema = createSchema({
  relationships: [
    productsRelationships,
    productDocumentRelationships,
    generatedResearchRelationships,
    savedResearchRelationships,
    companyContentRelationships,
    companyCampaignRelationships,
    icpRelationships,
    personaRelationships,
    companyBrandRelationships,
    ayrshareUserProfileRelationships,
    ayrshareSocialProfilesRelationships,
    companyTaskStatusesRelationships,
    postEngagementDetailsRelationships,
    siteResearchRelationships,
    socialsResearchRelationships,
    membershipsRelationships,
    subscriptionsRelationships
  ],
  tables: [
    accounts,
    icps,
    campaign_templates,
    post_templates,
    memberships,
    subscriptions,
    saved_research,
    generated_research,
    user_cache,
    products,
    product_documents,
    company_campaigns,
    company_content,
    personas,
    company_brand,
    ayrshare_user_profile,
    ayrshare_social_profiles,
    company_task_statuses,
    post_engagement_details,
    site_research,
    socials_research,
  ],
  // tables: [accounts, invitations, memberships],
});

// Typesafe schema type for client use
export type Schema = typeof schema;

// Permissions: Basic setup - will need to be expanded with proper team access
export const permissions = definePermissions<AuthData, Schema>(schema, () => {
  // Check if user is the primary owner of an account
  const allowIfOwner = (
    authData: AuthData,
    {cmp}: ExpressionBuilder<Schema, 'accounts'>,
  ) => {
    console.log('authData', authData.sub);
    return cmp('primary_owner_user_id', '=', authData.sub);
  };

  const allowIfProductDocumentIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'product_documents'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

    const allowIfUserIsInCompany = (
      authData: AuthData,
      { exists }: ExpressionBuilder<Schema, 'accounts_memberships'>,
    ) =>
      // Check if there exists a membership in the same company
      // that belongs to the currently authenticated user.
      exists('companyMemberships', q =>
        q.where('user_id', '=', authData.sub)
      );
  
    // For user_cache table - only allow users to see their own cache
  const allowIfUserOwnsCache = (
    authData: AuthData,
    {cmp}: ExpressionBuilder<Schema, 'user_cache'>,
  ) => {
    return cmp('user_id', '=', authData.sub);
  };

  // For accounts_memberships - allow users to see their own memberships
  const allowIfOwnMembership = (
    authData: AuthData,
    {cmp}: ExpressionBuilder<Schema, 'accounts_memberships'>,
  ) => {
    return cmp('user_id', '=', authData.sub);
  };


  // For generated_research - allow users to see research they created
  const allowIfGeneratedResearchIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'generated_research'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

    // For generated_research - allow users to see research they created
  const allowIfSavedResearchIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'saved_research'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

  const allowIfCompanyContentIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'company_content'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

  const allowIfCompanyCampaignIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'company_campaigns'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

  const allowIfIcpIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'icps'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

  const allowIfPersonasIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'personas'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

  const allowIfCompanyBrandIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'company_brand'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

  const allowIfSiteResearchIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'site_research'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

  const allowIfAyrshareUserProfileIsCompany = (
    authData: AuthData,
    { exists, cmp, or, and }: ExpressionBuilder<Schema, 'ayrshare_user_profile'>,
  ) =>
    and(
      // User must be a member of the company
      exists('memberships', q =>
        q.where('user_id', '=', authData.sub)
      ),
      // AND either profile is shared OR user owns the profile
      or(
        cmp('is_shared', '=', true),
        cmp('user_id', '=', authData.sub)
      )
    );

  const allowIfAyrshareSocialProfilesIsCompany = (
    authData: AuthData,
    { exists, cmp, or, and }: ExpressionBuilder<Schema, 'ayrshare_social_profiles'>,
  ) =>
    and(
      // User must be a member of the company
      exists('memberships', q =>
        q.where('user_id', '=', authData.sub)
      ),
      // AND either profile is shared OR user owns the profile
      or(
        cmp('is_shared', '=', true),
        cmp('user_id', '=', authData.sub)
      )
    );

  const allowIfCompanyTaskStatusesIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'company_task_statuses'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

  const allowIfPostEngagementDetailsIfProfileIsShared = (
    authData: AuthData,
    { exists, or }: ExpressionBuilder<Schema, 'post_engagement_details'>,
  ) =>
    // if the profile is shared or the user is the owner/creator then allow the user to see the post engagement details
    or( 
      exists('ayrshare_user_profile', q =>
        q.where('is_shared', '=', true)
      ),
      exists('ayrshare_user_profile', q =>
        q.where('user_id', '=', authData.sub)
      ),
    );

  const allowIfSocialsResearchIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'socials_research'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

  const allowIfSubscriptionIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'subscriptions'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

  const allowIfProductIsCompany = (
    authData: AuthData,
    { exists }: ExpressionBuilder<Schema, 'products'>,
  ) =>
    exists('memberships', q =>
      q.where('user_id', '=', authData.sub)
    );

  return {
    accounts: {
      row: {
        select: [allowIfOwner],
      },
    },
    icps: {
      row: {
        select: [allowIfIcpIsCompany],
      },
    },
    campaign_templates: {
      row: {
        select: ANYONE_CAN,
      },
    },
    post_templates: {
      row: {
        select: ANYONE_CAN,
      },
    },
    accounts_memberships: {
      row: {
        select: [allowIfUserIsInCompany],
      },
    },
    saved_research: {
      row: {
        select: [allowIfSavedResearchIsCompany],
      },
    },
    generated_research: {
      row: {
        select: [allowIfGeneratedResearchIsCompany],
      },
    },
    user_cache: {
      row: {
        select: [allowIfUserOwnsCache],
      },
    },
    products: {
      row: {
        select: [allowIfProductIsCompany],
      },
    },
    product_documents: {
      row: {
        select: [allowIfProductDocumentIsCompany],
      },
    },
    company_campaigns: {
      row: {
        select: [allowIfCompanyCampaignIsCompany],
      },
    },
    company_content: {
      row: {
        select: [allowIfCompanyContentIsCompany],
      },
    },
    personas: {
      row: {
        select: [allowIfPersonasIsCompany],
      },
    },
    company_brand: {
      row: {
        select: [allowIfCompanyBrandIsCompany],
      },
    },
    ayrshare_user_profile: {
      row: {
        select: [allowIfAyrshareUserProfileIsCompany],
      },
    },
    ayrshare_social_profiles: {
      row: {
        select: [allowIfAyrshareSocialProfilesIsCompany],
      },
    },
    company_task_statuses: {
      row: {
        select: [allowIfCompanyTaskStatusesIsCompany],
      },
    },
    post_engagement_details: {
      row: {
        select: [allowIfPostEngagementDetailsIfProfileIsShared],
      },
    },
    site_research: {
      row: {
        select: [allowIfSiteResearchIsCompany],
      },
    },
    socials_research: {
      row: {
        select: [allowIfSocialsResearchIsCompany],
      },
    },
    subscriptions: {
      row: {
        select: [allowIfSubscriptionIsCompany],
      },
    },
  }
});
