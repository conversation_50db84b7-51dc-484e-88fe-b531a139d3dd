'use client';

import React from 'react';

import Link from 'next/link';
import { Package, Plus } from 'lucide-react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import EmptyState from '~/components/empty-state';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';

import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import type { Product } from '~/types/product';

export default function ProductInfoContent() {
  const { account } = useTeamAccountWorkspace();
  const zero = useZero();
  

  
  const [products] = useZeroQuery(zero.query.products);
  console.log({ products });
  // Fetch ICPs to display names
  const [icps] = useZeroQuery(zero.query.icps.where('company_id', account?.id), {
    ttl: "10m"
  });
  
  console.log({ products });

  if (!products?.length) {
    return (
      <div className="flex grow flex-row items-center justify-center">
        <EmptyState
          iconName="Package"
          title={'Product Catalog'}
          description={
            'Create and manage your product catalog. Add products with descriptions, features, and documentation to help the system understand your offerings.'
          }
          buttonText={'Add Your First Product'}
          action={`/home/<USER>/product-information/new`}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-10 py-10">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Product Catalog</h1>
          <p className="text-muted-foreground">Manage your products and their documentation</p>
        </div>
        <Link href={`/home/<USER>/product-information/new`}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </Link>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {products.map((product) => (
          <Link 
            key={product.id} 
            href={`/home/<USER>/product-information/${product.id}`}
            className="block"
          >
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    <Package className="h-5 w-5 text-blue-600" />
                    <CardTitle className="text-lg">{product.name}</CardTitle>
                  </div>
                </div>
                {product.description && (
                  <CardDescription className="line-clamp-2">
                    {product.description}
                  </CardDescription>
                )}
              </CardHeader>
              
              <CardContent>
                {product.target_audience && Array.isArray(product.target_audience) && product.target_audience.length > 0 && (
                  <div className="mb-2">
                    <span className="text-sm font-medium text-gray-700">Target ICPs: </span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {product.target_audience.map((icpId) => {
                        const icp = icps?.find(i => i.id === icpId);
                        return (
                          <span key={icpId} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                            {icp?.name || `ICP ${icpId.slice(0, 8)}`}
                          </span>
                        );
                      })}
                    </div>
                  </div>
                )}
                
                {product.key_features && Array.isArray(product.key_features) && product.key_features.length > 0 && (
                  <div>
                    <span className="text-sm font-medium text-gray-700">Key Features:</span>
                    <div className="mt-1 space-y-2">
                      {product.key_features.slice(0, 2).map((feature, index) => (
                        <div key={index} className="text-sm">
                          <div className="flex items-start">
                            <span className="mr-2 mt-1">•</span>
                            <div className="flex-1">
                              <span className="font-medium text-gray-800">{feature.name}</span>
                              {feature.value_prop && (
                                <p className="text-xs text-gray-600 mt-1 line-clamp-2">{feature.value_prop}</p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                      {product.key_features.length > 2 && (
                        <p className="text-gray-400 text-xs ml-4">
                          +{product.key_features.length - 2} more features
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
