import { PageBody } from '@kit/ui/page';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';
// local imports
import { IntegrationCategoryCard } from './_components/integration-category-card';
import { TeamAccountLayoutPageHeader } from '../_components/layout';
import { SettingsLayout } from '../_components/settings/settings-layout';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('teams:integrations:pageTitle');

  return {
    title,
  };
};

interface TeamAccountIntegrationsPageProps {
  params: Promise<{ account: string }>;
}

async function TeamAccountIntegrationsPage(props: TeamAccountIntegrationsPageProps) {
  const slug = (await props.params).account;

  const integrationCategories = [
    {
      title: 'Socials',
      description: 'Connect your social media accounts to publish and manage content across platforms.',
      href: `/home/<USER>/integrations/socials`,
      icons: [
        {
          src: '/images/LI-Logo.png',
          alt: 'LinkedIn'
        },
        {
          src: '/images/logo-black.png',
          alt: 'X (Twitter)',
          darkSrc: '/images/logo-white.png'
        }
      ]
    },
    {
      title: 'Communications',
      description: 'Integrate with communication tools to streamline team collaboration.',
      href: `/home/<USER>/integrations/communications`,
      icons: []
    },
    {
      title: 'Productivity',
      description: 'Connect productivity tools to enhance your workflow and scheduling.',
      href: `/home/<USER>/integrations/productivity`,
      icons: []
    }
  ];

  return (
    <SettingsLayout account={slug}>
      <TeamAccountLayoutPageHeader
        account={slug}
        title={'Integrations'}
        description={'Connect your account to external services'}
      />

      <PageBody>
        <div className="flex flex-col space-y-6 max-w-2xl">
          {integrationCategories.map((category) => (
            <IntegrationCategoryCard
              key={category.title}
              title={category.title}
              description={category.description}
              href={category.href}
              icons={category.icons}
            />
          ))}
        </div>
      </PageBody>
    </SettingsLayout>
  );
}

export default withI18n(TeamAccountIntegrationsPage);
