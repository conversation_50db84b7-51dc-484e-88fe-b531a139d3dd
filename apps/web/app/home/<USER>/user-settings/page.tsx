import { PageHeader } from '@kit/ui/page';
import { use } from 'react';

import UserSettingsPage, { generateMetadata } from '../../(user)/settings/page';
import { SettingsLayout } from '../_components/settings/settings-layout';

export { generateMetadata };

interface PageProps {
  params: Promise<{ account: string }>;
}

export default function Page({ params }: PageProps) {
  const account = use(params).account;
  
  return (
    <SettingsLayout account={account}>
      <PageHeader title={'User Settings'} description={''} />
      <UserSettingsPage />
    </SettingsLayout>
  );
}
