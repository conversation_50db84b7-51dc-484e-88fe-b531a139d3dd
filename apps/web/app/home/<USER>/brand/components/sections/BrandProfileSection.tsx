'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Edit2, Save, X, Plus, Trash2 } from 'lucide-react';
import { BrandProfile } from '../../types/brand';
import { useZero } from '~/hooks/use-zero';

interface BrandProfileSectionProps {
  brandProfile: BrandProfile;
  brandId: string;
}

export function BrandProfileSection({ brandProfile, brandId }: BrandProfileSectionProps) {
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<Record<string, any>>({});
  const [isAddingCustomField, setIsAddingCustomField] = useState(false);
  const [customFieldName, setCustomFieldName] = useState('');
  const [attributeInputValue, setAttributeInputValue] = useState('');
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  const zero = useZero();

  useEffect(() => {
    if (editingField && inputRef.current) {
      inputRef.current.focus();
    }
  }, [editingField]);

  const handleEditStart = (field: string, value: any) => {
    setEditingField(field);
    setEditValues({ ...editValues, [field]: value });
  };

  const handleEditSave = (field: string) => {
    console.log(`Saving ${field}:`, editValues[field]);
    console.log(`editValues `, editValues);
    console.log(`brandProfile `, brandProfile);
    console.log(`To Save `, {
      ...brandProfile,
      [field]: editValues[field]
    });
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        brand_profile: {
          ...brandProfile,
          [field]: editValues[field]
        }
      }
    });
    setEditingField(null);
  };

  const handleEditCancel = () => {
    setEditingField(null);
    setEditValues({});
  };

  const handleAddCustomField = () => {
    if (customFieldName.trim() && !isStandardField(customFieldName)) {
      setEditingField(customFieldName);
      setEditValues({ ...editValues, [customFieldName]: '' });
      setIsAddingCustomField(false);
      setCustomFieldName('');
      console.log('Added custom field:', customFieldName);
      zero.mutate.company_brand.update({
        id: brandId,
        values: {
          brand_profile: {
            ...brandProfile,
            [customFieldName]: ''
          }
        }
      });
    }
  };

  const handleCustomFieldCancel = () => {
    setIsAddingCustomField(false);
    setCustomFieldName('');
  };

  const handleDeleteField = (field: string) => {
    if (field === 'mission' || field === 'vision') {
      return; // Cannot delete mission or vision
    }
    
    console.log(`Deleting field: ${field}`);
    const updatedProfile = { ...brandProfile };
    delete updatedProfile[field];
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        brand_profile: updatedProfile
      }
    });
  };

  const canDeleteField = (field: string) => {
    return field !== 'mission' && field !== 'vision';
  };

  const isStandardField = (fieldName: string) => {
    const standardFields = ['mission', 'vision', 'tagline', 'brand_attributes', 'core_offerings', 'roadmap_summary'];
    return standardFields.includes(fieldName);
  };

  const getCustomFields = () => {
    const standardFields = ['mission', 'vision', 'tagline', 'brand_attributes', 'core_offerings', 'roadmap_summary'];
    return Object.keys(brandProfile).filter(key => !standardFields.includes(key));
  };

  const renderEditableField = (
    field: string,
    label: string,
    value: any,
    isTextarea: boolean = false,
    helpText?: string
  ) => {
    const isEditing = editingField === field;
    
    return (
      <div className="mb-6">
        <div className="flex items-center justify-between mb-1">
          <label className="text-sm font-medium text-gray-700">{label}</label>
          {!isEditing && (
            <div className="flex items-center gap-1">
              <button
                onClick={() => handleEditStart(field, value)}
                className="text-gray-400 hover:text-gray-600 p-1"
              >
                <Edit2 size={14} />
              </button>
              {canDeleteField(field) && (
                <button
                  onClick={() => handleDeleteField(field)}
                  className="text-gray-400 hover:text-red-600 p-1"
                  title={`Delete ${label}`}
                >
                  <Trash2 size={14} />
                </button>
              )}
            </div>
          )}
        </div>
        
        {helpText && (
          <p className="text-xs text-gray-500 mb-2">{helpText}</p>
        )}
        
        {isEditing ? (
          <div className="space-y-2">
            {isTextarea ? (
              <textarea
                ref={inputRef as React.RefObject<HTMLTextAreaElement>}
                value={editValues[field] || ''}
                onChange={(e) => setEditValues({ ...editValues, [field]: e.target.value })}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={4}
              />
            ) : (
              <input
                ref={inputRef as React.RefObject<HTMLInputElement>}
                type="text"
                value={editValues[field] || ''}
                onChange={(e) => setEditValues({ ...editValues, [field]: e.target.value })}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            )}
            <div className="flex gap-2">
              <button
                onClick={() => handleEditSave(field)}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Save size={14} />
                Save
              </button>
              <button
                onClick={handleEditCancel}
                className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
              >
                <X size={14} />
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div 
            className="p-3 bg-gray-50 rounded-lg min-h-[44px] flex items-center cursor-pointer hover:bg-gray-100 transition-colors"
            onClick={() => handleEditStart(field, value)}
          >
            <span className="text-gray-900">
              {value ? (
                typeof value === 'object' ? (
                  Array.isArray(value) ? (
                    <div className="space-y-2">
                      {value.map((item, index) => (
                        <div key={index} className="flex items-start gap-3">
                          {typeof item === 'object' && item.name ? (
                            <>
                              <span className="font-medium text-gray-900 min-w-0 flex-shrink-0">
                                {item.name}:
                              </span>
                              <span className="text-gray-700">{item.description || item.value || 'No description'}</span>
                            </>
                          ) : (
                            <span className="text-gray-700">{typeof item === 'object' ? JSON.stringify(item) : item}</span>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {Object.entries(value).map(([key, val]) => (
                        <div key={key} className="flex items-start gap-3">
                          <span className="font-medium text-gray-900 min-w-0 flex-shrink-0">
                            {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                          </span>
                          <span className="text-gray-700">
                            {typeof val === 'object' ? JSON.stringify(val) : String(val)}
                          </span>
                        </div>
                      ))}
                    </div>
                  )
                ) : (
                  value
                )
              ) : (
                <span className="text-gray-400 italic">Not set</span>
              )}
            </span>
          </div>
        )}
      </div>
    );
  };

  const renderCoreOfferingsSection = () => {
    const isEditing = editingField === 'core_offerings';
    const offerings = brandProfile.core_offerings || [];
    
    return (
      <div className="mb-6">
        <div className="flex items-center justify-between mb-1">
          <label className="text-sm font-medium text-gray-700">Core Offerings</label>
          {!isEditing && (
            <div className="flex items-center gap-1">
              <button
                onClick={() => handleEditStart('core_offerings', offerings)}
                className="text-gray-400 hover:text-gray-600 p-1"
              >
                <Edit2 size={14} />
              </button>
              {canDeleteField('core_offerings') && (
                <button
                  onClick={() => handleDeleteField('core_offerings')}
                  className="text-gray-400 hover:text-red-600 p-1"
                  title="Delete Core Offerings"
                >
                  <Trash2 size={14} />
                </button>
              )}
            </div>
          )}
        </div>
        
        <p className="text-xs text-gray-500 mb-2">Your main products, services, or solutions</p>
        
        {isEditing ? (
          <div className="space-y-2">
            <div className="space-y-4">
              {(editValues.core_offerings || []).map((offering: any, index: number) => (
                <div key={index} className="p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <input
                      type="text"
                      value={offering.name || ''}
                      onChange={(e) => {
                        const newOfferings = [...(editValues.core_offerings || [])];
                        newOfferings[index] = { ...offering, name: e.target.value };
                        setEditValues({ ...editValues, core_offerings: newOfferings });
                      }}
                      placeholder="Offering name"
                      className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-medium"
                    />
                    <button
                      onClick={() => {
                        const newOfferings = (editValues.core_offerings || []).filter((_: any, i: number) => i !== index);
                        setEditValues({ ...editValues, core_offerings: newOfferings });
                      }}
                      className="ml-2 p-2 text-red-500 hover:text-red-700"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                  <textarea
                    value={offering.description || ''}
                    onChange={(e) => {
                      const newOfferings = [...(editValues.core_offerings || [])];
                      newOfferings[index] = { ...offering, description: e.target.value };
                      setEditValues({ ...editValues, core_offerings: newOfferings });
                    }}
                    placeholder="Description of this offering"
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                  />
                </div>
              ))}
            </div>
            <button
              onClick={() => {
                const newOfferings = [...(editValues.core_offerings || []), { name: '', description: '' }];
                setEditValues({ ...editValues, core_offerings: newOfferings });
              }}
              className="flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 text-sm"
            >
              <Plus size={14} />
              Add Offering
            </button>
            <div className="flex gap-2">
              <button
                onClick={() => handleEditSave('core_offerings')}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Save size={14} />
                Save
              </button>
              <button
                onClick={handleEditCancel}
                className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
              >
                <X size={14} />
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div 
            className="p-3 bg-gray-50 rounded-lg min-h-[44px] cursor-pointer hover:bg-gray-100 transition-colors"
            onClick={() => handleEditStart('core_offerings', offerings)}
          >
            {offerings.length ? (
              <div className="space-y-4">
                {offerings.map((offering, index) => (
                  <div key={index} className="p-3 bg-white rounded-lg border border-gray-200">
                    <h4 className="font-medium text-gray-900 mb-2">{offering.name}</h4>
                    <p className="text-gray-700 text-sm">{offering.description}</p>
                  </div>
                ))}
              </div>
            ) : (
              <span className="text-gray-400 italic">No offerings set</span>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderRoadmapSummarySection = () => {
    const isEditing = editingField === 'roadmap_summary';
    const roadmap = brandProfile.roadmap_summary || {};
    
    return (
      <div className="mb-6">
        <div className="flex items-center justify-between mb-1">
          <label className="text-sm font-medium text-gray-700">Roadmap Summary</label>
          {!isEditing && (
            <div className="flex items-center gap-1">
              <button
                onClick={() => handleEditStart('roadmap_summary', roadmap)}
                className="text-gray-400 hover:text-gray-600 p-1"
              >
                <Edit2 size={14} />
              </button>
              {canDeleteField('roadmap_summary') && (
                <button
                  onClick={() => handleDeleteField('roadmap_summary')}
                  className="text-gray-400 hover:text-red-600 p-1"
                  title="Delete Roadmap Summary"
                >
                  <Trash2 size={14} />
                </button>
              )}
            </div>
          )}
        </div>
        
        <p className="text-xs text-gray-500 mb-2">Key milestones and timeline for your company&apos;s development</p>
        
        {isEditing ? (
          <div className="space-y-2">
            <div className="space-y-4">
              {Object.entries(editValues.roadmap_summary || {}).map(([period, description], index) => (
                <div key={period} className="p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <input
                      type="text"
                      value={period}
                      onChange={(e) => {
                        const newRoadmap = { ...(editValues.roadmap_summary || {}) };
                        if (e.target.value !== period) {
                          delete newRoadmap[period];
                          newRoadmap[e.target.value] = description;
                        }
                        setEditValues({ ...editValues, roadmap_summary: newRoadmap });
                      }}
                      placeholder="Time period (e.g., Q1 2024, 2024, Phase 1)"
                      className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-medium"
                    />
                    <button
                      onClick={() => {
                        const newRoadmap = { ...(editValues.roadmap_summary || {}) };
                        delete newRoadmap[period];
                        setEditValues({ ...editValues, roadmap_summary: newRoadmap });
                      }}
                      className="ml-2 p-2 text-red-500 hover:text-red-700"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                  <textarea
                    value={description as string || ''}
                    onChange={(e) => {
                      const newRoadmap = { ...(editValues.roadmap_summary || {}) };
                      newRoadmap[period] = e.target.value;
                      setEditValues({ ...editValues, roadmap_summary: newRoadmap });
                    }}
                    placeholder="What's planned for this period"
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                  />
                </div>
              ))}
            </div>
            <button
              onClick={() => {
                const newRoadmap = { ...(editValues.roadmap_summary || {}) };
                const newPeriod = `Period ${Object.keys(newRoadmap).length + 1}`;
                newRoadmap[newPeriod] = '';
                setEditValues({ ...editValues, roadmap_summary: newRoadmap });
              }}
              className="flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 text-sm"
            >
              <Plus size={14} />
              Add Period
            </button>
            <div className="flex gap-2">
              <button
                onClick={() => handleEditSave('roadmap_summary')}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Save size={14} />
                Save
              </button>
              <button
                onClick={handleEditCancel}
                className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
              >
                <X size={14} />
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div 
            className="p-3 bg-gray-50 rounded-lg min-h-[44px] cursor-pointer hover:bg-gray-100 transition-colors"
            onClick={() => handleEditStart('roadmap_summary', roadmap)}
          >
            {Object.keys(roadmap).length ? (
              <div className="space-y-3">
                {Object.entries(roadmap).map(([period, description]) => (
                  <div key={period} className="p-3 bg-white rounded-lg border border-gray-200">
                    <h4 className="font-medium text-gray-900 mb-2">{period}</h4>
                    <p className="text-gray-700 text-sm">{description as string}</p>
                  </div>
                ))}
              </div>
            ) : (
              <span className="text-gray-400 italic">No roadmap set</span>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderBrandAttributes = () => {
    const isEditing = editingField === 'brand_attributes';
    
    const handleAttributeAdd = (value: string) => {
      if (value.trim()) {
        const currentAttributes = editValues.brand_attributes || [];
        if (!currentAttributes.includes(value.trim())) {
          setEditValues({ 
            ...editValues, 
            brand_attributes: [...currentAttributes, value.trim()] 
          });
        }
        setAttributeInputValue('');
      }
    };

    const handleAttributeRemove = (indexToRemove: number) => {
      const currentAttributes = editValues.brand_attributes || [];
      setEditValues({
        ...editValues,
        brand_attributes: currentAttributes.filter((_, index) => index !== indexToRemove)
      });
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleAttributeAdd(attributeInputValue);
      }
    };

    const handleEditStart = () => {
      setEditingField('brand_attributes');
      setEditValues({ 
        ...editValues, 
        brand_attributes: brandProfile.brand_attributes ? [...brandProfile.brand_attributes] : [] 
      });
      setAttributeInputValue('');
    };
    
    return (
      <div className="mb-6">
        <div className="flex items-center justify-between mb-1">
          <label className="text-sm font-medium text-gray-700">Brand Attributes</label>
          {!isEditing && (
            <div className="flex items-center gap-1">
              <button
                onClick={handleEditStart}
                className="text-gray-400 hover:text-gray-600 p-1"
              >
                <Edit2 size={14} />
              </button>
              <button
                onClick={() => handleDeleteField('brand_attributes')}
                className="text-gray-400 hover:text-red-600 p-1"
                title="Delete Brand Attributes"
              >
                <Trash2 size={14} />
              </button>
            </div>
          )}
        </div>
        
        <p className="text-xs text-gray-500 mb-2">Key characteristics and personality traits that define your brand</p>
        
        {isEditing ? (
          <div className="space-y-3">
            {/* Input for adding new attributes */}
            <div className="flex gap-2">
              <input
                ref={editingField === 'brand_attributes' ? inputRef as React.RefObject<HTMLInputElement> : undefined}
                type="text"
                value={attributeInputValue}
                onChange={(e) => setAttributeInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type attribute and press Enter to add"
                className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={() => handleAttributeAdd(attributeInputValue)}
                disabled={!attributeInputValue.trim()}
                className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                Add
              </button>
            </div>
            
            {/* Display current attributes as editable pills */}
            {editValues.brand_attributes?.length > 0 && (
              <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg">
                {editValues.brand_attributes.map((attr, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                  >
                    {attr}
                    <button
                      onClick={() => handleAttributeRemove(index)}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      <X size={12} />
                    </button>
                  </span>
                ))}
              </div>
            )}
            
            <div className="flex gap-2">
              <button
                onClick={() => handleEditSave('brand_attributes')}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Save size={14} />
                Save
              </button>
              <button
                onClick={handleEditCancel}
                className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
              >
                <X size={14} />
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div 
            className="p-3 bg-gray-50 rounded-lg min-h-[44px] cursor-pointer hover:bg-gray-100 transition-colors"
            onClick={handleEditStart}
          >
            {brandProfile.brand_attributes?.length ? (
              <div className="flex flex-wrap gap-2">
                {brandProfile.brand_attributes.map((attr, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                  >
                    {attr}
                  </span>
                ))}
              </div>
            ) : (
              <span className="text-gray-400 italic">No attributes set</span>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderCustomFields = () => {
    const customFields = getCustomFields();
    
    return (
      <>
        {customFields.map(field => (
          <div key={field}>
            {renderEditableField(field, field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()), brandProfile[field])}
          </div>
        ))}
      </>
    );
  };

  const renderAddCustomFieldForm = () => {
    return (
      <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Custom Field Name</label>
            <input
              type="text"
              value={customFieldName}
              onChange={(e) => setCustomFieldName(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleAddCustomField();
                }
              }}
              placeholder="Enter field name (e.g., company_history, core_values)"
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={handleAddCustomField}
              disabled={!customFieldName.trim() || isStandardField(customFieldName)}
              className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <Plus size={14} />
              Add Field
            </button>
            <button
              onClick={handleCustomFieldCancel}
              className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
            >
              <X size={14} />
              Cancel
            </button>
          </div>
          
          {customFieldName.trim() && isStandardField(customFieldName) && (
            <p className="text-sm text-red-600">This field name is already used. Please choose a different name.</p>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Brand Profile</h1>
        <p className="text-gray-600">Define your brand&apos;s core identity and values</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {renderEditableField('mission', 'Mission Statement', brandProfile.mission, true, 'Why your organization exists and its fundamental purpose')}
        {renderEditableField('vision', 'Vision Statement', brandProfile.vision, true, 'What your organization aspires to achieve in the future')}
        {renderEditableField('tagline', 'Tagline', brandProfile.tagline, false, 'A memorable phrase that captures your brand essence')}
        {renderBrandAttributes()}
        
        {/* Complex Object Fields with Specialized Editors */}
        {brandProfile.core_offerings && renderCoreOfferingsSection()}
        {brandProfile.roadmap_summary && renderRoadmapSummarySection()}
        
        {/* Custom Fields */}
        {renderCustomFields()}
        
        {/* Add Custom Field Form */}
        {isAddingCustomField && renderAddCustomFieldForm()}
        
        {/* Add Custom Field Button */}
        {!isAddingCustomField && !editingField && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <button
              onClick={() => setIsAddingCustomField(true)}
              className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <Plus size={16} />
              Add Custom Field
            </button>
          </div>
        )}
      </div>
    </div>
  );
}