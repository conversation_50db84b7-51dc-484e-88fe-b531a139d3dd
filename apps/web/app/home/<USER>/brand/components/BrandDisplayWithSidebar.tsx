'use client';

import React, { useState } from 'react';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { CompanyBrand } from '../types/brand';
import { BrandNavigationSidebar } from './BrandNavigationSidebar';
import { BrandProfileSection } from './sections/BrandProfileSection';
import { MessagingStrategySection } from './sections/MessagingStrategySection';
import { VisualIdentitySection } from './sections/VisualIdentitySection';

import { PromptLibrarySection } from './sections/PromptLibrarySection';
import { SettingsSection } from './sections/SettingsSection';

interface BrandDisplayWithSidebarProps {
  companyBrand: CompanyBrand;
}

export function BrandDisplayWithSidebar({ companyBrand }: BrandDisplayWithSidebarProps) {
  const [activeSection, setActiveSection] = useState('brand-profile');

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'brand-profile':
        return <BrandProfileSection brandId={companyBrand.id} brandProfile={companyBrand.brand_profile} />;
      case 'messaging-strategy':
        return <MessagingStrategySection brandId={companyBrand.id} messagingStrategy={companyBrand.messaging_strategy} />;
      case 'visual-identity':
        return <VisualIdentitySection brandId={companyBrand.id} visualIdentity={companyBrand.visual_identity} />;
      // case 'prompt-library':
      //   return <PromptLibrarySection brandId={companyBrand.id} prompts={companyBrand.prompt_library} />;
      case 'settings':
        return <SettingsSection brandId={companyBrand.id} brandName={companyBrand.brand_name} />;
      default:
        return <BrandProfileSection brandId={companyBrand.id} brandProfile={companyBrand.brand_profile} />;
    }
  };

  return (
    <div className="flex h-full bg-gray-50">
      <BrandNavigationSidebar
        activeSection={activeSection}
        onSectionChange={setActiveSection}
      />
      <div className="flex-1 overflow-auto">
        <div className="p-8">
          {renderActiveSection()}
        </div>
      </div>
    </div>
  );
}