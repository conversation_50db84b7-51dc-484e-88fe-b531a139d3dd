import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { addDays, formatISO } from 'date-fns';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';
import { Database } from '@kit/supabase/database';

import type { DeleteInvitationSchema } from '../../schema/delete-invitation.schema';
import type { InviteMembersSchema } from '../../schema/invite-members.schema';
import type { UpdateInvitationSchema } from '../../schema/update-invitation.schema';

export function createAccountInvitationsService(
  client: SupabaseClient<Database>,
) {
  return new AccountInvitationsService(client);
}

/**
 * @name AccountInvitationsService
 * @description Service for managing account invitations.
 */
class AccountInvitationsService {
  private readonly namespace = 'invitations';

  constructor(private readonly client: SupabaseClient<Database>) {}

  /**
   * @name deleteInvitation
   * @description Removes an invitation from the database.
   * @param params
   */
  async deleteInvitation(params: z.infer<typeof DeleteInvitationSchema>) {
    const logger = await getLogger();

    const ctx = {
      name: this.namespace,
      ...params,
    };

    logger.info(ctx, 'Removing invitation...');

    const { data, error } = await this.client
      .from('invitations')
      .delete()
      .match({
        id: params.invitationId,
      });

    if (error) {
      logger.error(ctx, `Failed to remove invitation`);

      throw error;
    }

    logger.info(ctx, 'Invitation successfully removed');

    return data;
  }

  /**
   * @name updateInvitation
   * @param params
   * @description Updates an invitation in the database.
   */
  async updateInvitation(params: z.infer<typeof UpdateInvitationSchema>) {
    const logger = await getLogger();

    const ctx = {
      name: this.namespace,
      ...params,
    };

    logger.info(ctx, 'Updating invitation...');

    const { data, error } = await this.client
      .from('invitations')
      .update({
        role: params.role,
      })
      .match({
        id: params.invitationId,
      });

    if (error) {
      logger.error(
        {
          ...ctx,
          error,
        },
        'Failed to update invitation',
      );

      throw error;
    }

    logger.info(ctx, 'Invitation successfully updated');

    return data;
  }

  async validateInvitation(
    invitation: z.infer<typeof InviteMembersSchema>['invitations'][number],
    accountSlug: string,
  ) {
    const { data: members, error } = await this.client.rpc(
      'get_account_members',
      {
        account_slug: accountSlug,
      },
    );

    if (error) {
      throw error;
    }

    const isUserAlreadyMember = members.find((member) => {
      return member.email === invitation.email;
    });

    if (isUserAlreadyMember) {
      throw new Error('User already member of the team');
    }
  }

  /**
   * @name sendInvitations
   * @description Sends invitations to join a team.
   * @param accountSlug
   * @param invitations
   */
  async sendInvitations({
    accountSlug,
    invitations,
  }: {
    invitations: z.infer<typeof InviteMembersSchema>['invitations'];
    accountSlug: string;
  }) {
    const logger = await getLogger();

    const ctx = {
      accountSlug,
      name: this.namespace,
    };

    logger.info(ctx, 'Storing invitations...');

    try {
      await Promise.all(
        invitations.map((invitation) =>
          this.validateInvitation(invitation, accountSlug),
        ),
      );
    } catch (error) {
      logger.error(
        {
          ...ctx,
          error: (error as Error).message,
        },
        'Error validating invitations',
      );

      throw error;
    }

    const accountResponse = await this.client
      .from('accounts')
      .select('name, id')
      .eq('slug', accountSlug)
      .single();

    if (!accountResponse.data) {
      logger.error(
        ctx,
        'Account not found in database. Cannot send invitations.',
      );

      throw new Error('Account not found');
    }

    // Store invitations in database first
    const response = await this.client.rpc('add_invitations_to_account', {
      invitations,
      account_slug: accountSlug,
    });

    if (response.error) {
      logger.error(
        {
          ...ctx,
          error: response.error,
        },
        `Failed to add invitations to account ${accountSlug}`,
      );

      throw response.error;
    }

    const responseInvitations = Array.isArray(response.data)
      ? response.data
      : [response.data];

    logger.info(
      {
        ...ctx,
        count: responseInvitations.length,
      },
      'Invitations added to account',
    );

    // Send batch emails directly instead of relying on database triggers
    try {
      await this.sendBatchInvitationEmails({
        invitations: responseInvitations,
        accountName: accountResponse.data.name,
        accountId: accountResponse.data.id,
      });

      logger.info(
        {
          ...ctx,
          count: responseInvitations.length,
        },
        'Batch invitation emails sent successfully',
      );
    } catch (error) {
      logger.error(
        {
          ...ctx,
          error: (error as Error).message,
        },
        'Failed to send batch invitation emails',
      );

      // Mark invitations as failed for potential retry
      await this.markInvitationsAsEmailFailed(responseInvitations.map(inv => inv.id));

      // Note: We don't throw here to avoid rolling back the database changes
      // The invitations are stored, and we can retry sending emails later
      // or they will be sent via the webhook fallback mechanism
    }

    return { data: responseInvitations };
  }

  /**
   * @name sendBatchInvitationEmails
   * @description Sends batch invitation emails using Resend's batch API
   */
  private async sendBatchInvitationEmails({
    invitations,
    accountName,
    accountId,
  }: {
    invitations: any[];
    accountName: string;
    accountId: string;
  }) {
    const logger = await getLogger();

    const ctx = {
      name: this.namespace,
      accountId,
      count: invitations.length,
    };

    logger.info(ctx, 'Preparing batch invitation emails...');

    // Get environment variables
    const env = this.getEmailEnvironment();

    // Get the inviter information
    const inviterResponse = await this.client
      .from('accounts')
      .select('email, name')
      .eq('id', invitations[0]?.invited_by)
      .single();

    if (inviterResponse.error) {
      logger.error(
        {
          ...ctx,
          error: inviterResponse.error,
        },
        'Failed to fetch inviter details',
      );
      throw inviterResponse.error;
    }

    const inviterName = inviterResponse.data.name ?? inviterResponse.data.email ?? '';

    // Prepare batch emails
    const { renderInviteEmail } = await import('@kit/email-templates');
    const { getMailer } = await import('@kit/mailers');

    const mailer = await getMailer();

    // Check if mailer supports batch emails
    if (!mailer.sendBatchEmails) {
      logger.warn(ctx, 'Mailer does not support batch emails, falling back to individual sends');

      const failedInvitations: number[] = [];

      // Fallback to individual emails with delay to avoid rate limits
      for (const invitation of invitations) {
        try {
          await this.sendSingleInvitationEmail({
            invitation,
            inviterName,
            accountName,
            env,
          });

          logger.info(
            {
              ...ctx,
              invitationId: invitation.id,
            },
            'Individual invitation email sent successfully',
          );

          // Add a small delay between emails to avoid rate limits
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          logger.error(
            {
              ...ctx,
              invitationId: invitation.id,
              error: (error as Error).message,
            },
            'Failed to send individual invitation email',
          );

          failedInvitations.push(invitation.id);
        }
      }

      if (failedInvitations.length > 0) {
        logger.error(
          {
            ...ctx,
            failedCount: failedInvitations.length,
            failedInvitations,
          },
          'Some individual invitation emails failed to send',
        );

        throw new Error(`Failed to send ${failedInvitations.length} out of ${invitations.length} invitation emails`);
      }

      return;
    }

    // Prepare all emails for batch sending
    const batchEmails = await Promise.all(
      invitations.map(async (invitation) => {
        const link = this.getInvitationLink(invitation.invite_token, invitation.email, env.siteURL);

        const { html, subject } = await renderInviteEmail({
          link,
          invitedUserEmail: invitation.email,
          inviter: inviterName,
          productName: env.productName,
          teamName: accountName,
        });

        return {
          from: env.emailSender,
          to: invitation.email,
          subject,
          html,
        };
      })
    );

    // Send batch emails
    try {
      const batchResult = await mailer.sendBatchEmails({ emails: batchEmails });

      logger.info(
        {
          ...ctx,
          batchResult,
        },
        'Batch invitation emails sent successfully',
      );
    } catch (error) {
      logger.error(
        {
          ...ctx,
          error: (error as Error).message,
        },
        'Batch email sending failed',
      );

      // If batch fails, we could implement a fallback to individual sends
      // For now, we'll throw the error to be handled by the calling method
      throw error;
    }
  }

  /**
   * @name sendSingleInvitationEmail
   * @description Sends a single invitation email (fallback method)
   */
  private async sendSingleInvitationEmail({
    invitation,
    inviterName,
    accountName,
    env,
  }: {
    invitation: any;
    inviterName: string;
    accountName: string;
    env: {
      siteURL: string;
      productName: string;
      emailSender: string;
    };
  }) {
    const { renderInviteEmail } = await import('@kit/email-templates');
    const { getMailer } = await import('@kit/mailers');

    const mailer = await getMailer();
    const link = this.getInvitationLink(invitation.invite_token, invitation.email, env.siteURL);

    const { html, subject } = await renderInviteEmail({
      link,
      invitedUserEmail: invitation.email,
      inviter: inviterName,
      productName: env.productName,
      teamName: accountName,
    });

    await mailer.sendEmail({
      from: env.emailSender,
      to: invitation.email,
      subject,
      html,
    });
  }

  /**
   * @name getEmailEnvironment
   * @description Get email-related environment variables
   */
  private getEmailEnvironment() {
    const siteURL = process.env.NEXT_PUBLIC_SITE_URL;
    const productName = process.env.NEXT_PUBLIC_PRODUCT_NAME ?? '';
    const emailSender = process.env.EMAIL_SENDER;

    return z
      .object({
        siteURL: z
          .string({
            required_error: 'NEXT_PUBLIC_SITE_URL is required',
          })
          .min(1),
        productName: z
          .string({
            required_error: 'NEXT_PUBLIC_PRODUCT_NAME is required',
          })
          .min(1),
        emailSender: z
          .string({
            required_error: 'EMAIL_SENDER is required',
          })
          .min(1),
      })
      .parse({
        siteURL,
        productName,
        emailSender,
      });
  }

  /**
   * @name getInvitationLink
   * @description Generate invitation link
   */
  private getInvitationLink(inviteToken: string, email: string, siteURL: string) {
    const invitePath = '/join';
    const url = new URL(invitePath, siteURL);

    url.searchParams.set('token', inviteToken);
    url.searchParams.set('email', email);

    return url.toString();
  }

  /**
   * @name markInvitationsAsEmailFailed
   * @description Mark invitations as having failed email delivery for potential retry
   */
  private async markInvitationsAsEmailFailed(invitationIds: number[]) {
    const logger = await getLogger();

    const ctx = {
      name: this.namespace,
      invitationIds,
    };

    logger.info(ctx, 'Marking invitations as email failed...');

    try {
      // Add a metadata field to track email failures
      // Note: This assumes the invitations table has a metadata jsonb column
      // If not, you could create a separate table to track email status
      const { error } = await this.client
        .from('invitations')
        .update({
          // Using a generic approach that should work with most schemas
          // You might need to adjust this based on your actual table structure
          updated_at: new Date().toISOString(),
        })
        .in('id', invitationIds);

      if (error) {
        logger.error(
          {
            ...ctx,
            error,
          },
          'Failed to mark invitations as email failed',
        );
      } else {
        logger.info(ctx, 'Successfully marked invitations as email failed');
      }
    } catch (error) {
      logger.error(
        {
          ...ctx,
          error: (error as Error).message,
        },
        'Error marking invitations as email failed',
      );
    }
  }

  /**
   * @name acceptInvitationToTeam
   * @description Accepts an invitation to join a team.
   */
  async acceptInvitationToTeam(
    adminClient: SupabaseClient<Database>,
    params: {
      userId: string;
      inviteToken: string;
    },
  ) {
    const logger = await getLogger();
    const ctx = {
      name: this.namespace,
      ...params,
    };

    logger.info(ctx, 'Accepting invitation to team');

    const { error, data } = await adminClient.rpc('accept_invitation', {
      token: params.inviteToken,
      user_id: params.userId,
    });

    if (error) {
      logger.error(
        {
          ...ctx,
          error,
        },
        'Failed to accept invitation to team',
      );

      throw error;
    }

    logger.info(ctx, 'Successfully accepted invitation to team');

    return data;
  }

  /**
   * @name renewInvitation
   * @description Renews an invitation to join a team by extending the expiration date by 7 days.
   * @param invitationId
   */
  async renewInvitation(invitationId: number) {
    const logger = await getLogger();

    const ctx = {
      invitationId,
      name: this.namespace,
    };

    logger.info(ctx, 'Renewing invitation...');

    const sevenDaysFromNow = formatISO(addDays(new Date(), 7));

    const { data, error } = await this.client
      .from('invitations')
      .update({
        expires_at: sevenDaysFromNow,
      })
      .match({
        id: invitationId,
      });

    if (error) {
      logger.error(
        {
          ...ctx,
          error,
        },
        'Failed to renew invitation',
      );

      throw error;
    }

    logger.info(ctx, 'Invitation successfully renewed');

    return data;
  }
}
