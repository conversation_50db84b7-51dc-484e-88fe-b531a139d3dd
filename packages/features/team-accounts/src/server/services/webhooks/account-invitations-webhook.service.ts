import { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';
import { Database } from '@kit/supabase/database';

type Invitation = Database['public']['Tables']['invitations']['Row'];

const invitePath = '/join';

const siteURL = process.env.NEXT_PUBLIC_SITE_URL;
const productName = process.env.NEXT_PUBLIC_PRODUCT_NAME ?? '';
const emailSender = process.env.EMAIL_SENDER;

const env = z
  .object({
    invitePath: z
      .string({
        required_error: 'The property invitePath is required',
      })
      .min(1),
    siteURL: z
      .string({
        required_error: 'NEXT_PUBLIC_SITE_URL is required',
      })
      .min(1),
    productName: z
      .string({
        required_error: 'NEXT_PUBLIC_PRODUCT_NAME is required',
      })
      .min(1),
    emailSender: z
      .string({
        required_error: 'EMAIL_SENDER is required',
      })
      .min(1),
  })
  .parse({
    invitePath,
    siteURL,
    productName,
    emailSender,
  });

export function createAccountInvitationsWebhookService(
  client: SupabaseClient<Database>,
) {
  return new AccountInvitationsWebhookService(client);
}

class AccountInvitationsWebhookService {
  private namespace = 'accounts.invitations.webhook';

  constructor(private readonly adminClient: SupabaseClient<Database>) {}

  /**
   * @name handleInvitationWebhook
   * @description Handles the webhook event for invitations
   * @param invitation
   */
  async handleInvitationWebhook(invitation: Invitation) {
    const logger = await getLogger();

    const ctx = {
      invitationId: invitation.id,
      name: this.namespace,
    };

    logger.info(ctx, 'Received invitation webhook event');

    // Check if this invitation was created as part of a batch operation
    // If so, emails might have already been sent via the batch API
    const recentInvitations = await this.adminClient
      .from('invitations')
      .select('id, created_at')
      .eq('account_id', invitation.account_id)
      .gte('created_at', new Date(Date.now() - 5000).toISOString()) // Within last 5 seconds
      .order('created_at', { ascending: false });

    if (recentInvitations.data && recentInvitations.data.length > 1) {
      logger.info(
        {
          ...ctx,
          batchSize: recentInvitations.data.length,
        },
        'Multiple invitations detected within short timeframe, likely batch operation. Skipping individual email send.',
      );

      return {
        success: true,
        skipped: true,
        reason: 'batch_operation_detected',
      };
    }

    // Single invitation or fallback - send individual email
    return this.dispatchInvitationEmail(invitation);
  }

  private async dispatchInvitationEmail(invitation: Invitation) {
    const logger = await getLogger();

    logger.info(
      { invitation, name: this.namespace },
      'Handling invitation webhook event...',
    );

    const inviter = await this.adminClient
      .from('accounts')
      .select('email, name')
      .eq('id', invitation.invited_by)
      .single();

    if (inviter.error) {
      logger.error(
        {
          error: inviter.error,
          name: this.namespace,
        },
        'Failed to fetch inviter details',
      );

      throw inviter.error;
    }

    const team = await this.adminClient
      .from('accounts')
      .select('name')
      .eq('id', invitation.account_id)
      .single();

    if (team.error) {
      logger.error(
        {
          error: team.error,
          name: this.namespace,
        },
        'Failed to fetch team details',
      );

      throw team.error;
    }

    const ctx = {
      invitationId: invitation.id,
      name: this.namespace,
    };

    logger.info(ctx, 'Invite retrieved. Sending invitation email...');

    try {
      const { renderInviteEmail } = await import('@kit/email-templates');
      const { getMailer } = await import('@kit/mailers');

      const mailer = await getMailer();
      const link = this.getInvitationLink(
        invitation.invite_token,
        invitation.email,
      );

      const { html, subject } = await renderInviteEmail({
        link,
        invitedUserEmail: invitation.email,
        inviter: inviter.data.name ?? inviter.data.email ?? '',
        productName: env.productName,
        teamName: team.data.name,
      });

      await mailer
        .sendEmail({
          from: env.emailSender,
          to: invitation.email,
          subject,
          html,
        })
        .then(() => {
          logger.info(ctx, 'Individual invitation email successfully sent!');
        })
        .catch((error) => {
          logger.error({ error, ...ctx }, 'Failed to send individual invitation email');

          // Don't throw the error to avoid webhook retry loops
          // The invitation is already stored in the database
        });

      return {
        success: true,
      };
    } catch (error) {
      logger.warn({ error, ...ctx }, 'Failed to invite user to team');

      return {
        error,
        success: false,
      };
    }
  }

  private getInvitationLink(token: string, email: string) {
    const searchParams = new URLSearchParams({
      invite_token: token,
      email,
    }).toString();

    const href = new URL(env.invitePath, env.siteURL).href;

    return `${href}?${searchParams}`;
  }
}
